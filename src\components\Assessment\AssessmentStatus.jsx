import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import apiService from '../../services/apiService';
import { useNotifications } from '../../hooks/useNotifications';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';
import ConnectionStatus from '../UI/ConnectionStatus';

const AssessmentStatus = () => {
  const { jobId } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentStage, setCurrentStage] = useState('transforming'); // transforming, analyzing, preparing
  const pollingIntervalRef = useRef(null);

  // Setup notifications with callbacks
  const { isConnected, isAuthenticated, notifications, clearNotification } = useNotifications({
    onAnalysisComplete: (data) => {
      console.log('🔔 Analysis complete callback triggered:', data);
      console.log('🔍 Current jobId:', jobId);
      console.log('🔍 Received jobId:', data.jobId);

      if (data.jobId === jobId) {
        console.log('✅ JobId matches, proceeding with redirect');
        // Set stage to preparing report
        setCurrentStage('preparing');

        // Stop polling and redirect to results after a brief delay to show the final stage
        if (pollingIntervalRef.current) {
          console.log('🛑 Clearing polling interval');
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }

        // Show "Preparing your personalized report" for 2 seconds before redirecting
        setTimeout(() => {
          console.log('🚀 Navigating to results page:', `/results/${data.resultId}`);
          navigate(`/results/${data.resultId}`);
        }, 2000);
      } else {
        console.log('❌ JobId mismatch, not redirecting');
      }
    },
    onAnalysisFailed: (data) => {
      console.log('🔔 Analysis failed callback triggered:', data);
      if (data.jobId === jobId) {
        // Stop polling and show error
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
          pollingIntervalRef.current = null;
        }
        setError(data.message || 'Analysis failed');
      }
    }
  });

  const checkStatus = async () => {
    try {
      const response = await apiService.getAssessmentStatus(jobId);

      if (response.success) {
        const statusData = response.data;
        setStatus(statusData);

        // Update stage based on status
        if (statusData.status === 'queued') {
          setCurrentStage('transforming');
        } else if (statusData.status === 'processing') {
          setCurrentStage('analyzing');
        } else if (statusData.status === 'completed') {
          // Don't redirect here - wait for websocket notification
          // The websocket will handle the final stage and redirect
          setCurrentStage('preparing');
        } else if (statusData.status === 'failed') {
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
          setError('Analysis failed. Please try again.');
        }
      }
    } catch (err) {
      console.error('Error checking status:', err);
      setError(err.response?.data?.message || 'Failed to check status');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!jobId) {
      navigate('/assessment');
      return;
    }

    // Initial status check
    checkStatus();

    // Setup polling as fallback (every 5 seconds)
    const interval = setInterval(checkStatus, 5000);
    pollingIntervalRef.current = interval;

    // Cleanup
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [jobId]);

  const getStageInfo = (stage) => {
    switch (stage) {
      case 'transforming':
        return {
          title: 'Transforming your Data',
          description: 'We are preparing and organizing your assessment responses for analysis. Our system is securely processing your data and getting it ready for our AI algorithms.',
          color: 'text-amber-600',
          bgColor: 'from-amber-200 to-amber-300',
          iconColor: 'text-amber-600'
        };
      case 'analyzing':
        return {
          title: 'Analyzing with latest AI Technology',
          description: 'Our advanced AI is now analyzing your responses using cutting-edge algorithms to create your comprehensive talent profile and career recommendations.',
          color: 'text-blue-600',
          bgColor: 'from-blue-200 to-blue-300',
          iconColor: 'text-blue-600'
        };
      case 'preparing':
        return {
          title: 'Preparing your personalized report',
          description: 'Your analysis is complete! We are now generating your personalized talent mapping report with detailed insights and career recommendations.',
          color: 'text-emerald-600',
          bgColor: 'from-emerald-200 to-emerald-300',
          iconColor: 'text-emerald-600'
        };
      default:
        return {
          title: 'Processing Assessment',
          description: 'Your assessment is being processed...',
          color: 'text-gray-600',
          bgColor: 'from-gray-200 to-gray-300',
          iconColor: 'text-gray-600'
        };
    }
  };



  const getStageIcon = (stage) => {
    const stageInfo = getStageInfo(stage);

    switch (stage) {
      case 'transforming':
        return (
          <div className="relative">
            <div className="animate-pulse">
              <div className={`h-16 w-16 bg-gradient-to-br ${stageInfo.bgColor} rounded-full shadow-lg`}></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <svg className={`h-8 w-8 ${stageInfo.iconColor}`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        );
      case 'analyzing':
        return (
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-100 border-t-blue-600 shadow-lg"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <svg className={`h-8 w-8 ${stageInfo.iconColor}`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        );
      case 'preparing':
        return (
          <div className="relative">
            <div className={`h-16 w-16 bg-gradient-to-br ${stageInfo.bgColor} rounded-full flex items-center justify-center shadow-lg ring-4 ring-emerald-50 animate-pulse`}>
              <svg className={`h-8 w-8 ${stageInfo.iconColor}`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        );
      default:
        return (
          <div className="h-16 w-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full shadow-lg"></div>
        );
    }
  };

  if (isLoading && !status) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="relative mb-8">
            {getStageIcon('transforming')}
          </div>
          <div className="space-y-4">
            <h2 className="text-3xl font-bold text-amber-600">Transforming your Data</h2>
            <p className="text-lg text-gray-600 max-w-md mx-auto">Please wait while we prepare your assessment data for analysis...</p>
            <div className="flex justify-center">
              <div className="animate-bounce flex space-x-1">
                <div className="w-2 h-2 bg-amber-600 rounded-full"></div>
                <div className="w-2 h-2 bg-amber-600 rounded-full animation-delay-100"></div>
                <div className="w-2 h-2 bg-amber-600 rounded-full animation-delay-200"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8">
      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-10">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full shadow-lg mb-6">
            <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clipRule="evenodd" />
            </svg>
          </div>
          <h1 className="text-5xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-4">
            Assessment Analysis
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">Your talent mapping assessment is being processed with advanced AI algorithms</p>
        </div>

        {/* Notifications */}
        <div className="space-y-4 mb-8">
          {notifications.map((notification) => (
            <div key={notification.id} className={`p-5 rounded-2xl shadow-lg backdrop-blur-sm border transition-all duration-300 hover:shadow-xl ${
              notification.type === 'success'
                ? 'bg-emerald-50/80 border-emerald-200 hover:bg-emerald-50'
                : 'bg-red-50/80 border-red-200 hover:bg-red-50'
            }`}>
              <div className="flex justify-between items-start">
                <div className="flex items-start space-x-4">
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center shadow-sm ${
                    notification.type === 'success' ? 'bg-emerald-100' : 'bg-red-100'
                  }`}>
                    {notification.type === 'success' ? (
                      <svg className="w-5 h-5 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className={`text-base font-semibold ${
                      notification.type === 'success' ? 'text-emerald-800' : 'text-red-800'
                    }`}>
                      {notification.title}
                    </h3>
                    <p className={`mt-1 text-sm ${
                      notification.type === 'success' ? 'text-emerald-700' : 'text-red-700'
                    }`}>
                      {notification.message}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => clearNotification(notification.id)}
                  className="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-full hover:bg-white/50"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Main Status Card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-200/50 overflow-hidden">
          {error ? (
            <div className="p-10">
              <ErrorMessage
                title="Assessment Error"
                message={error}
                onRetry={() => navigate('/assessment')}
                retryText="Start New Assessment"
              />
            </div>
          ) : status ? (
            <div className="p-10">
              {/* Progress Steps */}
              <div className="mb-10">
                <div className="flex justify-center items-center space-x-4 mb-8">
                  {/* Step 1: Transforming */}
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      currentStage === 'transforming' ? 'bg-amber-600 text-white' :
                      ['analyzing', 'preparing'].includes(currentStage) ? 'bg-emerald-600 text-white' :
                      'bg-gray-300 text-gray-600'
                    }`}>
                      1
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      currentStage === 'transforming' ? 'text-amber-600' :
                      ['analyzing', 'preparing'].includes(currentStage) ? 'text-emerald-600' :
                      'text-gray-500'
                    }`}>
                      Transforming Data
                    </span>
                  </div>

                  {/* Arrow */}
                  <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>

                  {/* Step 2: Analyzing */}
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      currentStage === 'analyzing' ? 'bg-blue-600 text-white' :
                      currentStage === 'preparing' ? 'bg-emerald-600 text-white' :
                      'bg-gray-300 text-gray-600'
                    }`}>
                      2
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      currentStage === 'analyzing' ? 'text-blue-600' :
                      currentStage === 'preparing' ? 'text-emerald-600' :
                      'text-gray-500'
                    }`}>
                      AI Analysis
                    </span>
                  </div>

                  {/* Arrow */}
                  <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>

                  {/* Step 3: Preparing */}
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      currentStage === 'preparing' ? 'bg-emerald-600 text-white' :
                      'bg-gray-300 text-gray-600'
                    }`}>
                      3
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      currentStage === 'preparing' ? 'text-emerald-600' : 'text-gray-500'
                    }`}>
                      Preparing Report
                    </span>
                  </div>
                </div>
              </div>

              {/* Status Header */}
              <div className="text-center mb-10">
                <div className="flex justify-center mb-8">
                  {getStageIcon(currentStage)}
                </div>

                {/* Stage Title and Description */}
                <div className="space-y-4">
                  <h2 className={`text-3xl font-bold mb-4 ${getStageInfo(currentStage).color}`}>
                    {getStageInfo(currentStage).title}
                  </h2>

                  <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
                    {getStageInfo(currentStage).description}
                  </p>
                </div>
              </div>

              {/* Progress Section */}
              {status.progress && (
                <div className="mb-10">
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-sm font-semibold text-gray-700">Analysis Progress</span>
                    <span className="text-sm font-bold text-indigo-600 bg-indigo-50 px-3 py-1 rounded-full">
                      {Math.round(status.progress)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden shadow-inner">
                    <div
                      className="h-full bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-600 rounded-full transition-all duration-1000 ease-out relative"
                      style={{ width: `${status.progress}%` }}
                    >
                      <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>
              )}

              {/* Time Estimation */}
              {status.estimatedTimeRemaining && (
                <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl p-6 mb-8 border border-indigo-100">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-indigo-900">Estimated time remaining</p>
                      <p className="text-2xl font-bold text-indigo-700">{status.estimatedTimeRemaining}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Connection Status & Job Info */}
              <div className="border-t border-gray-200/50 pt-8">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200/50">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <div className="w-2 h-2 bg-green-500 rounded-full mr-3 animate-pulse"></div>
                      Connection Status
                    </h3>
                    <ConnectionStatus
                      isConnected={isConnected}
                      isAuthenticated={isAuthenticated}
                    />
                  </div>

                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-6 border border-gray-200/50">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      Job Information
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Job ID:</span>
                        <span className="text-sm font-mono text-gray-900 bg-gray-200 px-2 py-1 rounded">
                          {jobId?.slice(0, 8)}...
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Current Stage:</span>
                        <span className={`text-sm font-semibold px-3 py-1 rounded-full ${
                          currentStage === 'transforming' ? 'bg-amber-100 text-amber-700' :
                          currentStage === 'analyzing' ? 'bg-blue-100 text-blue-700' :
                          currentStage === 'preparing' ? 'bg-emerald-100 text-emerald-700' :
                          'bg-gray-100 text-gray-700'
                        }`}>
                          {getStageInfo(currentStage).title}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-4 mt-10">
                <button
                  onClick={() => navigate('/dashboard')}
                  className="px-8 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-all duration-200 font-medium shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
                >
                  Return to Dashboard
                </button>
                {status.status === 'failed' && (
                  <button
                    onClick={() => navigate('/assessment')}
                    className="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    Try Again
                  </button>
                )}
              </div>
            </div>
          ) : (
            <div className="p-10 text-center">
              <LoadingSpinner size="lg" text="Checking status..." />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssessmentStatus;